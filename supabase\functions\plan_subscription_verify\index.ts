import { serve } from "https://deno.land/std/http/server.ts";
import { createClient } from "npm:@supabase/supabase-js";
import { corsHeaders } from "../_shared/cors.ts";

// Supabase setup
const supabaseUrl = Deno.env.get("SUPABASE_URL");
const supabaseServiceRoleKey = Deno.env.get("SUPABASE_SERVICE_ROLE_KEY");

if (!supabaseUrl || !supabaseServiceRoleKey) {
  throw new Error("Missing Supabase environment variables");
}

const supabase = createClient(supabaseUrl, supabaseServiceRoleKey);

serve(async (req: Request) => {
  // Handle CORS preflight
  if (req.method === "OPTIONS") {
    return new Response("ok", { headers: corsHeaders });
  }

  if (req.method !== "GET") {
    return new Response(JSON.stringify({ error: "Method not allowed" }), {
      status: 405,
      headers: corsHeaders,
    });
  }

  const url = new URL(req.url);
  const userId = url.searchParams.get("user_id");

  if (!userId) {
    return new Response(
      JSON.stringify({ error: "Missing user_id query parameter" }),
      { headers: corsHeaders, status: 400 }
    );
  }

  const { data, error } = await supabase
    .from("plans_subscription")
    .select("is_subscribed")
    .eq("user_id", userId)
    .single();

  if (error && error.code !== "PGRST116") {
    return new Response(JSON.stringify({ error: error.message }), {
      headers: corsHeaders,
      status: 500,
    });
  }

  const is_subscribed = data?.is_subscribed ?? false;

  return new Response(
    JSON.stringify({ is_subscribed }),
    {
      headers: { ...corsHeaders, "Content-Type": "application/json" },
    }
  );
});
