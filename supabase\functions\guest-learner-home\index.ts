import { createClient } from "npm:@supabase/supabase-js";
import { corsHeaders } from "../_shared/cors.ts";

// Initialize Supabase client
const supabaseUrl = Deno.env.get('SUPABASE_URL') ?? '';
const supabaseKey = Deno.env.get('SUPABASE_ANON_KEY') ?? '';
const supabase = createClient(supabaseUrl, supabaseKey);

console.log("supabaseUrl", supabaseUrl);
console.log("supabaseKey", supabaseKey);

async function handleRequest(request) {
  if (request.method === 'OPTIONS') {
    return new Response('ok', {
      headers: corsHeaders
    });
  }

  if (request.method !== "GET") {
    return new Response(JSON.stringify("Only GET method is accepted"), {
      status: 405,
      headers: {
        ...corsHeaders,
        "Content-Type": "application/json"
      }
    });
  }

  try {
    // Parse query parameters
    const url = new URL(request.url);
    const organization_id = url.searchParams.get('organization_id');
    const search = url.searchParams.get('search')?.trim(); // Add search parameter

    if (!organization_id) {
      return new Response(JSON.stringify({
        error: "Missing organization_id parameter"
      }), {
        status: 400,
        headers: {
          ...corsHeaders,
          "Content-Type": "application/json"
        }
      });
    }

    console.log("organization_id", organization_id);

    // Fetch banners from the organization table
    const { data: organizations, error: orgError } = await supabase
      .from('organization')
      .select('*')
      .eq('id', organization_id);

    if (orgError) {
      console.error('Error fetching banners:', orgError);
      throw orgError;
    }

    // Fetch tags from tags table
    const { data: tags, error: tagError } = await supabase
      .from('tags')
      .select('*')
      .eq('organization_id', organization_id); // Add additional filters as needed

    console.log('tags response:', {
      tags,
      tagError
    });

    if (tagError) {
      console.error('Error fetching tags:', tagError);
    } else {
      console.log('tags:', tags);
    }

    // Fetch learning paths with modules and their lessons
    let query = supabase
      .from('learning_path_module')
      .select(`
        learning_path_id, 
        module_id, 
        learning_path(
          id,
          title,
          description,
          imageUrl
        ),
        modules(
          id,
          duration,
          lessons(
            id
          )
        )
      `)
      .eq('organization_id', organization_id);

    // Add search filter if search parameter is provided
    if (search) {
      query = query.filter('learning_path.title', 'ilike', `%${search}%`);
    }

    const { data: learningPaths, error: learningPathError } = await query;

    if (learningPathError) {
      throw learningPathError;
    }

    console.log("learningPaths", learningPaths);

    // Extract learning path IDs from the filtered learning paths
    const learningPathIds = learningPaths.map((lp) => lp.learning_path_id);

    // Fetch all reviews for the filtered learning paths in a single query
    const { data: allReviews, error: reviewsError } = await supabase
      .from('reviews')
      .select('learning_path_id, rating')
      .in('learning_path_id', learningPathIds);

    if (reviewsError) {
      throw reviewsError;
    }

    console.log("All Reviews:", allReviews);

    // Group reviews by learning_path_id and calculate total rating, count, and review count
    const reviewsByLearningPath = allReviews.reduce((acc, review) => {
      if (!acc[review.learning_path_id]) {
        acc[review.learning_path_id] = {
          totalRating: 0,
          count: 0,
          reviewCount: 0
        };
      }
      acc[review.learning_path_id].totalRating += review.rating;
      acc[review.learning_path_id].count += 1;
      acc[review.learning_path_id].reviewCount += 1; // Increment review count
      return acc;
    }, {});

    // Calculate average ratings for each learning path
    const averageRatings = Object.entries(reviewsByLearningPath).reduce((acc, [learningPathId, { totalRating, count }]) => {
      acc[learningPathId] = count > 0 ? Math.round(totalRating / count) : 0; // Ensure division by zero is handled and round to whole number
      return acc;
    }, {});

    console.log("Average Ratings:", averageRatings);

    // Calculate module count for each learning path
    const moduleCounts = learningPaths.reduce((acc, lp) => {
      if (!acc[lp.learning_path_id]) {
        acc[lp.learning_path_id] = 0;
      }
      // Increment module count for this learning path
      acc[lp.learning_path_id] += 1;
      return acc;
    }, {});

    console.log("Module Counts:", moduleCounts);

    // Calculate total lesson count for each learning path (sum of all lessons across all modules)
    const lessonCounts = learningPaths.reduce((acc, lp) => {
      if (!acc[lp.learning_path_id]) {
        acc[lp.learning_path_id] = 0;
      }
      // Add the number of lessons in this module to the total for this learning path
      const lessonsInModule = lp.modules?.lessons?.length || 0;
      acc[lp.learning_path_id] += lessonsInModule;
      return acc;
    }, {});

    console.log("Lesson Counts:", lessonCounts);

    // Calculate total duration for each learning path (sum of all module durations)
    const totalDurations = learningPaths.reduce((acc, lp) => {
      if (!acc[lp.learning_path_id]) {
        acc[lp.learning_path_id] = 0;
      }
      // Add the duration of this module to the total for this learning path
      const moduleDuration = lp.modules?.duration || 0;
      acc[lp.learning_path_id] += moduleDuration;
      return acc;
    }, {});

    console.log("Total Durations:", totalDurations);

    // Hardcoded trending courses
    const trending_courses = [
      {
        "learning_path_id": "b66e6e5f-a590-4e18-93f3-19291e85acba",
        "title": "Introduction to Programming",
        "description": "Learn the basics of programming",
        "imageUrl": "https://images.unsplash.com/photo-1610563166150-b34df4f3bcd6?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80",
        "module_id": "b072cd6e-e122-4533-8076-50c11f41feee",
        "lesson_count": 12,
        "duration": 399,
        "avg_rating": 3,
        "review_count": 1
      },
      {
        "learning_path_id": "c77e7e6f-b690-5f28-84f4-29392f96bdcc",
        "title": "Advanced JavaScript",
        "description": "Master advanced JavaScript concepts",
        "imageUrl": "https://images.unsplash.com/photo-1579468118864-1b9ea3c0db4a?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80",
        "module_id": "c183de7f-f233-4644-9087-61d22f52gfff",
        "lesson_count": 24,
        "duration": 450,
        "avg_rating": 4,
        "review_count": 2
      },
      {
        "learning_path_id": "d88e8e7g-c790-6g39-95g5-39493g07ceee",
        "title": "Data Structures and Algorithms",
        "description": "Understand core data structures and algorithms",
        "imageUrl": "https://images.unsplash.com/photo-1515879218367-8466d910aaa4?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80",
        "module_id": "d294ef8g-g344-5755-a098-72e33g63hhhh",
        "lesson_count": 18,
        "duration": 300,
        "avg_rating": 5,
        "review_count": 3
      },
      {
        "learning_path_id": "e99e9e8h-d890-7h40-a6h6-49594h18diii",
        "title": "Web Development Fundamentals",
        "description": "Learn the basics of web development",
        "imageUrl": "https://images.unsplash.com/photo-1507238691740-187a5b1d37b8?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80",
        "module_id": "e3a5fgh9-h455-6866-b1a9-83f44h74iiii",
        "lesson_count": 15,
        "duration": 200,
        "avg_rating": 4,
        "review_count": 4
      },
      {
        "learning_path_id": "f10f0f9i-e990-8i51-b7i7-59605i29ejjj",
        "title": "Machine Learning Basics",
        "description": "Get started with machine learning",
        "imageUrl": "https://images.unsplash.com/photo-1507146153580-69a1fe6d8aa1?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80",
        "module_id": "f4b6gij0-i566-7977-c2b0-94g55i85jjjj",
        "lesson_count": 30,
        "duration": 500,
        "avg_rating": 5,
        "review_count": 5
      },
      {
        "learning_path_id": "g21g1g0j-f100-9j62-c8j8-69716j30fkkk",
        "title": "Cloud Computing Essentials",
        "description": "Learn the fundamentals of cloud computing",
        "imageUrl": "https://images.unsplash.com/photo-1516321318423-f06f85e504b3?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80",
        "module_id": "g5c7hjk1-j677-8088-d3c1-05h66j96kkkk",
        "lesson_count": 28,
        "duration": 600,
        "avg_rating": 4,
        "review_count": 6
      },
      {
        "learning_path_id": "h32h2h1k-g211-0k73-d9k9-79827k41glll",
        "title": "DevOps for Beginners",
        "description": "Understand the basics of DevOps",
        "imageUrl": "https://images.unsplash.com/photo-1523474253046-8cd2748b5fd2?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80",
        "module_id": "h6d8ikl2-k788-9199-e4d2-16i77k07llll",
        "lesson_count": 35,
        "duration": 700,
        "avg_rating": 5,
        "review_count": 7
      },
      {
        "learning_path_id": "i43i3i2l-h322-1l84-e0l0-89938l52hmmm",
        "title": "Cybersecurity Fundamentals",
        "description": "Learn the basics of cybersecurity",
        "imageUrl": "https://images.unsplash.com/photo-1563206767-5b18f218e8de?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80",
        "module_id": "i7e9jlm3-l899-0200-f5e3-27j88l18mmmm",
        "lesson_count": 42,
        "duration": 800,
        "avg_rating": 4,
        "review_count": 8
      },
      {
        "learning_path_id": "j54j4j3m-i433-2m95-f1m1-99049m63innn",
        "title": "Mobile App Development",
        "description": "Build your first mobile app",
        "imageUrl": "https://images.unsplash.com/photo-1519389950473-47ba0277781c?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80",
        "module_id": "j8f0kmn4-m900-1311-g6f4-38k99m29nnnn",
        "lesson_count": 38,
        "duration": 900,
        "avg_rating": 5,
        "review_count": 9
      },
      {
        "learning_path_id": "k65k5k4n-j544-3n06-g2n2-09150n74jooo",
        "title": "Artificial Intelligence Basics",
        "description": "Get started with AI",
        "imageUrl": "https://images.unsplash.com/photo-1516116216624-53e697fedbea?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80",
        "module_id": "k9g1lno5-n011-2422-h7g5-49l00n30oooo",
        "lesson_count": 45,
        "duration": 1000,
        "avg_rating": 4,
        "review_count": 10
      }
    ];

    // Prepare the response
    const response = {
      appData: organizations.map((banner) => banner.application_data),
      tags: tags.map((cat) => ({
        tag_name: cat.tag_name,
        images: cat.images,
        tag_id: cat.tag_id
      })),
      popular_courses: Array.from(
        new Map(
          learningPaths
            .filter((lp) => lp.learning_path !== null && lp.learning_path !== undefined) // Filter out null/undefined learning_path
            .map((lp) => ({
              id: lp.learning_path_id,
              learning_path_id: lp.learning_path_id,
              title: lp.learning_path?.title || "Untitled",
              description: lp.learning_path?.description || "",
              imageUrl: lp.learning_path?.imageUrl || "",
              module_id: lp.module_id,
              module_count: moduleCounts[lp.learning_path_id] || 0,
              lesson_count: lessonCounts[lp.learning_path_id] || 0, // Changed from module_count to lesson_count
              duration: totalDurations[lp.learning_path_id] || 0, // Use total duration across all modules
              avg_rating: Math.round(averageRatings[lp.learning_path_id] || 0),
              review_count: reviewsByLearningPath[lp.learning_path_id]?.reviewCount || 0,
              is_enrolled: false // Add is_enrolled field with default value false
            }))
            .map((course) => [course.learning_path_id, course])
        ).values()
      ),
      trending_courses: trending_courses // Add the hardcoded trending courses
    };

    return new Response(JSON.stringify(response), {
      status: 200,
      headers: {
        ...corsHeaders,
        "Content-Type": "application/json"
      }
    });
  } catch (error) {
    console.error("Error processing request:", error);
    return new Response(`Failed to process request: ${error.message}`, {
      status: 500,
      headers: {
        ...corsHeaders,
        "Content-Type": "application/json"
      }
    });
  }
}

// Listen for requests
await Deno.serve(handleRequest);