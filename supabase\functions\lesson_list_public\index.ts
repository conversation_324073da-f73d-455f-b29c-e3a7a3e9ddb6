import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2";
import { connect } from "https://deno.land/x/redis@v0.27.0/mod.ts";
const supabaseUrl = Deno.env.get("SUPABASE_URL") ?? "";
const supabaseKey = Deno.env.get("SUPABASE_ANON_KEY") ?? "";
const supabase = createClient(supabaseUrl, supabaseKey);
let redis;
try {
  redis = await connect({
    hostname: "eternal-midge-26406.upstash.io",
    port: 6379,
    password: "AWcmAAIjcDE5ZTQwYTJjOWEwMzQ0OTllYmU0N2FkODY5MTNlZTgwOXAxMA",
    tls: true
  });
  console.log("Connected to Upstash Redis");
} catch (err) {
  console.error("Failed to connect to Upstash Redis:", err);
  redis = null;
}
const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Methods": "GET, OPTIONS",
  "Access-Control-Allow-Headers": "x-client-info, apikey, content-type"
};
serve(async (req)=>{
  if (req.method === "OPTIONS") {
    return new Response("ok", {
      headers: corsHeaders
    });
  }
  if (req.method !== "GET") {
    return new Response(JSON.stringify({
      error: "Method not allowed"
    }), {
      status: 405,
      headers: {
        ...corsHeaders,
        "Content-Type": "application/json"
      }
    });
  }
  try {
    const url = new URL(req.url);
    const moduleId = url.searchParams.get("module_id");
    if (!moduleId) {
      return new Response(JSON.stringify({
        error: "Module ID is required"
      }), {
        status: 400,
        headers: {
          ...corsHeaders,
          "Content-Type": "application/json"
        }
      });
    }
    if (redis) {
      try {
        const cachedData = await redis.get(moduleId);
        if (cachedData) {
          console.log(`Cache hit for module ${moduleId}`);
          return new Response(cachedData, {
            headers: {
              ...corsHeaders,
              "Content-Type": "application/json"
            },
            status: 200
          });
        }
      } catch (err) {
        console.error("Upstash Redis get error:", err);
      }
    }
    const { data: module, error: moduleError } = await supabase.from("modules").select(`
        id,
        title,
        description,
        image_url,
        banner_image_url,
        duration,
        lesson_count,
        is_live,
        is_active,
        is_certificate_required,
        about_the_course,
        lessons (
          id,
          title,
          description,
          lesson_level,
          lesson_type,
          duration,
          content_url,
          is_active
        )
      `).eq("id", moduleId).single();
    if (moduleError) {
      return new Response(JSON.stringify({
        error: "Failed to fetch module data"
      }), {
        status: 500,
        headers: {
          ...corsHeaders,
          "Content-Type": "application/json"
        }
      });
    }
    if (!module) {
      return new Response(JSON.stringify({
        error: "Module not found"
      }), {
        status: 404,
        headers: {
          ...corsHeaders,
          "Content-Type": "application/json"
        }
      });
    }
    const processedLessons = module.lessons.sort((a, b)=>a.lesson_level - b.lesson_level).map((lesson, index)=>({
        ...lesson,
        isLocked: index >= 2
      }));
    const response = {
      id: module.id,
      title: module.title,
      description: module.description,
      image_url: module.image_url,
      banner_image_url: module.banner_image_url,
      duration: module.duration,
      lesson_count: module.lesson_count,
      is_live: module.is_live,
      is_active: module.is_active,
      is_certificate_required: module.is_certificate_required,
      about_the_course: module.about_the_course,
      language: "English",
      updated_at: "23 dec, 2024",
      lessons: processedLessons
    };
    if (redis) {
      try {
        await redis.setex(moduleId, 3600, JSON.stringify(response));
        console.log(`Cache set for module ${moduleId} in Upstash Redis with 1-hour TTL`);
      } catch (err) {
        console.error("Upstash Redis set error:", err);
      }
    }
   return new Response(JSON.stringify({
  data: response
}), {
  status: 200,
  headers: {
    ...corsHeaders,
    "Content-Type": "application/json"
  }
});

  } catch (error) {
    return new Response(JSON.stringify({
      error: "Unexpected error",
      details: error.message
    }), {
      status: 500,
      headers: {
        ...corsHeaders,
        "Content-Type": "application/json"
      }
    });
  }
});
