import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2";
// Supabase credentials
const supabaseUrl = Deno.env.get("SUPABASE_URL") ?? "";
const supabaseKey = Deno.env.get("SUPABASE_ANON_KEY") ?? "";
const supabase = createClient(supabaseUrl, supabaseKey);
// CORS headers
const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Methods": "GET, OPTIONS",
  "Access-Control-Allow-Headers": "authorization, x-client-info, apikey, content-type"
};
serve(async (req)=>{
  // Handle CORS preflight
  if (req.method === "OPTIONS") {
    return new Response("ok", {
      headers: corsHeaders
    });
  }
  // Only allow GET requests
  if (req.method !== "GET") {
    return new Response(JSON.stringify({
      error: "Method not allowed"
    }), {
      status: 405,
      headers: {
        ...corsHeaders,
        "Content-Type": "application/json"
      }
    });
  }
  try {
    // Parse URL for query parameters
    const url = new URL(req.url);
    const learningPathId = url.searchParams.get("learning_path_id");
    const search = url.searchParams.get("search");
    if (!learningPathId) {
      return new Response(JSON.stringify({
        error: "Missing learning_path_id parameter"
      }), {
        status: 400,
        headers: {
          ...corsHeaders,
          "Content-Type": "application/json"
        }
      });
    }
    // Fetch learning path details
    const { data: learningPath, error: learningPathError } = await supabase.from("learning_path").select(`
        id,
        created_at,
        updated_at,
        organization_id,
        title,
        description,
        is_published,
        imageUrl,
        is_active,
        start_date,
        end_date,
        course_subtitle,
        difficulty_level,
        course_duration
      `).eq("id", learningPathId).single();
    if (learningPathError || !learningPath) {
      return new Response(JSON.stringify({
        error: "Failed to fetch learning path data",
        details: learningPathError
      }), {
        status: 500,
        headers: {
          ...corsHeaders,
          "Content-Type": "application/json"
        }
      });
    }
    // Fetch modules associated with the learning path
    const { data: learningPathModules, error: modulesError } = await supabase.from("learning_path_module").select(`
        id,
        created_at,
        learning_path_id,
        module_id,
        module_level,
        is_active,
        is_live,
        is_certificate_required,
        modules (
          id,
          title,
          description,
          duration,
          lesson_count,
          is_active,
          is_certificate_required,
          image_url,
          slug,
          module_code,
          no_of_pdf,
          no_of_video,
          no_of_quiz,
          banner_image_url,
          tags,
          is_live,
          source,
          about_the_course,
          announcements
        )
      `).eq("learning_path_id", learningPathId).order("module_level", {
      ascending: true
    });
    if (modulesError) {
      return new Response(JSON.stringify({
        error: "Failed to fetch module data",
        details: modulesError
      }), {
        status: 500,
        headers: {
          ...corsHeaders,
          "Content-Type": "application/json"
        }
      });
    }
    // Process and structure the response data with isLocked logic
    const processedModules = [];
    const processedModuleIds = new Set(); // Track processed module IDs to avoid duplicates
    let isPreviousModuleCompleted = true; // First module should be unlocked by default
    // Ensure we have modules data and it's an array
    const modules = Array.isArray(learningPathModules) ? learningPathModules : [];
    // Sort modules by module_level to ensure consistent order
    const sortedModules = [
      ...modules
    ].sort((a, b)=>(a.module_level || 0) - (b.module_level || 0));
    for (const module of sortedModules){
      // Skip if we've already processed this module_id
      if (processedModuleIds.has(module.module_id)) {
        console.log(`Skipping duplicate module: ${module.module_id}`);
        continue;
      }
      // Skip modules with missing data
      if (!module.modules) {
        console.log(`Skipping module with missing data: ${module.module_id}`);
        continue;
      }
      // Cast modules to Module interface to avoid TypeScript errors
      const moduleDetails = module.modules;
      // Apply search filter if a search query is provided
      if (search) {
        const lowerSearch = search.toLowerCase();
        const title = String(moduleDetails.title || "");
        const description = String(moduleDetails.description || "");
        if (!title.toLowerCase().includes(lowerSearch) && !description.toLowerCase().includes(lowerSearch)) {
          continue; // Skip this module if it does not match the search criteria
        }
      }
      // Create the module data object
      const moduleData = {
        id: module.id,
        created_at: module.created_at,
        learning_path_id: module.learning_path_id,
        module_id: module.module_id,
        module_level: module.module_level,
        is_active: module.is_active,
        is_live: module.is_live,
        is_certificate_required: module.is_certificate_required,
        module_details: {
          title: moduleDetails.title,
          description: moduleDetails.description,
          duration: moduleDetails.duration,
          lesson_count: moduleDetails.lesson_count,
          is_active: moduleDetails.is_active,
          is_certificate_required: moduleDetails.is_certificate_required,
          image_url: moduleDetails.image_url,
          slug: moduleDetails.slug,
          module_code: moduleDetails.module_code,
          no_of_pdf: moduleDetails.no_of_pdf,
          no_of_video: moduleDetails.no_of_video,
          no_of_quiz: moduleDetails.no_of_quiz,
          banner_image_url: moduleDetails.banner_image_url,
          tags: moduleDetails.tags,
          is_live: moduleDetails.is_live,
          source: moduleDetails.source,
          about_the_course: moduleDetails.about_the_course,
          announcements: moduleDetails.announcements,
          level: module.module_level || "Beginner"
        },
        isLocked: !isPreviousModuleCompleted
      };
      processedModules.push(moduleData);
      processedModuleIds.add(module.module_id); // Mark this module_id as processed
      // Update the flag for the next module
      isPreviousModuleCompleted = false; // Assume modules are locked unless explicitly marked as completed
    }
      const { count: moduleCount, error: moduleCountError } = await supabase
  .from("learning_path_module")
  .select("*", { count: "exact", head: true })
  .eq("learning_path_id", learningPathId);

if (moduleCountError) {
  return new Response(JSON.stringify({
    error: "Failed to fetch module count",
    details: moduleCountError
  }), {
    status: 500,
    headers: {
      ...corsHeaders,
      "Content-Type": "application/json"
    }
  });
}


  const { count: totalmoduleCount, error: totalmoduleCountError } = await supabase
  .from("learning_path_module")
  .select("*", { count: "exact", head: true })
  .eq("learning_path_id", learningPathId);

if (totalmoduleCountError) {
  return new Response(JSON.stringify({
    error: "Failed to fetch module count",
    details: totalmoduleCountError
  }), {
    status: 500,
    headers: {
      ...corsHeaders,
      "Content-Type": "application/json"
    }
  });
}

   // First get the module_ids from the learning_path_module table
const { data: modulesList, error: moduleListError } = await supabase
  .from("learning_path_module")
  .select("module_id")
  .eq("learning_path_id", learningPathId);

if (moduleListError) {
  return new Response(JSON.stringify({
    error: "Failed to fetch module list",
    details: moduleListError
  }), {
    status: 500,
    headers: {
      ...corsHeaders,
      "Content-Type": "application/json"
    }
  });
}

const moduleIdsForDuration = modulesList.map(m => m.module_id);

// Now fetch total duration
const { data: moduleDurations, error: moduleDurationError } = await supabase
  .from("modules")
  .select("duration")
  .in("id", moduleIdsForDuration);

if (moduleDurationError) {
  return new Response(JSON.stringify({
    error: "Failed to fetch module durations",
    details: moduleDurationError
  }), {
    status: 500,
    headers: {
      ...corsHeaders,
      "Content-Type": "application/json"
    }
  });
}

// Sum the durations
const totalModuleDuration = moduleDurations.reduce(
  (sum, module) => sum + (module.duration || 0),
  0
);
    // Return the response
    return new Response(JSON.stringify({
      success: true,
      data: {
        learning_path: learningPath,
         total_modules: totalmoduleCount,
         module_duration: totalModuleDuration,
        modules: processedModules
      }
    }), {
      status: 200,
      headers: {
        ...corsHeaders,
        "Content-Type": "application/json"
      }
    });
  } catch (error) {
    // Handle errors
    const errorMessage = error instanceof Error ? error.message : "An unknown error occurred";
    return new Response(JSON.stringify({
      error: "Unexpected error",
      details: errorMessage
    }), {
      status: 500,
      headers: {
        ...corsHeaders,
        "Content-Type": "application/json"
      }
    });
  }
});
