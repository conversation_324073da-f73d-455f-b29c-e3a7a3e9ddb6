// supabase/functions/update_learning_path_from_csv_debug.ts
import { serve } from "https://deno.land/std@0.192.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js";
import { parse } from "https://deno.land/std@0.192.0/csv/mod.ts";

serve(async (req) => {
  let body;
  try {
    body = await req.json();
  } catch {
    return new Response(JSON.stringify({ error: "Invalid JSON body. Must include 'file'." }), { status: 400 });
  }

  const fileName = body?.file;
  if (!fileName) {
    return new Response(JSON.stringify({ error: "Missing 'file' in request body." }), { status: 400 });
  }

  const supabase = createClient(
    Deno.env.get("SUPABASE_URL")!,
    Deno.env.get("SUPABASE_SERVICE_ROLE_KEY")!
  );

  const bucket = "avatars";

  // 1. List files in the bucket (debug)
  const { data: files, error: listError } = await supabase.storage.from(bucket).list();
  if (listError) {
    return new Response(JSON.stringify({ error: "Error listing files", details: listError }), { status: 500 });
  }

  // 2. Download the CSV file
  const { data: fileData, error: downloadError } = await supabase.storage.from(bucket).download(fileName);
  if (downloadError) {
    return new Response(JSON.stringify({ error: "Download error", details: downloadError }), { status: 500 });
  }

  const csvText = await fileData.text();

  // 3. Return CSV content & list of files (debug info)
  if (!csvText || csvText.trim() === "") {
    return new Response(
      JSON.stringify({
        error: "CSV file is empty",
        filesInBucket: files,
      }),
      { status: 400 }
    );
  }

  // 4. Parse CSV with headers
  let records: { [key: string]: string }[] = [];
  try {
    records = await parse(csvText, {
      skipFirstRow: false,
      columns: true,
    }) as { [key: string]: string }[];
  } catch (err) {
    return new Response(JSON.stringify({ error: `CSV parse error: ${err.message}` }), { status: 400 });
  }

  // 5. Prepare updates (keep id as UUID string)
  const updates = records.map((r) => ({
    id: r.id,
    course_overview: r.course_overview,
  }));

  // 6. Upsert into learning_path table
  const { error: upsertError } = await supabase
    .from("learning_path")
    .upsert(updates, { onConflict: ["id"] });

  if (upsertError) {
    return new Response(JSON.stringify({ error: `Upsert error: ${upsertError.message}` }), { status: 500 });
  }

  return new Response(
    JSON.stringify({
      message: "learning_path updated successfully",
      updatedRecords: updates.length,
      filesInBucket: files,
      csvPreview: csvText.slice(0, 500), // first 500 chars of CSV for debug
    }),
    { headers: { "Content-Type": "application/json" } }
  );
});
